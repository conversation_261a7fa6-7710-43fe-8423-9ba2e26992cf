<template>
  <view v-for="(item, index) in times" :key="index">
    <view
      @click="handleSelectTime(item, index)"
      :class="[
        'px-3.5 py-2 text-sm transition-all duration-300 box-border border border-gray-200 rounded-lg flex items-center justify-center cursor-pointer',
        getTimeItemClass(item, index)
      ]"
    >
      <view class="flex flex-col items-center">
        <text class="text-xs">{{ item.time }}</text>
        <text v-if="item.count !== undefined" class="text-xs mt-1 opacity-75">
          剩余{{ item.count }}个
        </text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';
  import { HomeTypes } from '@/monkey/types';

  const props = withDefaults(
    defineProps<{
      times: HomeTypes.InboundTimeItem[];
      type: 'am' | 'pm';
      selectedIndex?: number | null;
    }>(),
    {
      times: () => [],
      type: 'am',
      selectedIndex: null,
    },
  );

  const emit = defineEmits<{
    (e: 'selectTime', item: HomeTypes.InboundTimeItem, index: number): void;
  }>();

  /**
   * 处理时间选择
   */
  const handleSelectTime = (item: HomeTypes.InboundTimeItem, index: number) => {
    // 如果时间段已禁用或过期，不允许选择
    if (item.isDisabled || item.isExpired || item.count === 0) {
      const message = item.isExpired
        ? '该时间段已过期'
        : item.isDisabled
        ? '该时间段已禁用'
        : '该时间段已满';
      monkey.$helper.toast.error(message);
      return;
    }

    emit('selectTime', item, index);
  };

  /**
   * 获取时间项的样式类
   */
  const getTimeItemClass = (item: HomeTypes.InboundTimeItem, index: number) => {
    // 如果已禁用或过期或已满
    if (item.isDisabled || item.isExpired || item.count === 0) {
      return 'bg-gray-100 text-gray-400 cursor-not-allowed opacity-50';
    }

    // 如果是选中状态
    if (props.selectedIndex === index) {
      return 'bg-theme-blue text-white shadow-md';
    }

    // 默认状态
    return 'bg-gray-50 text-gray-700 hover:bg-gray-100 active:bg-gray-200';
  };
</script>

<template>
  <view v-for="(item, index) in times" :key="index">
    <view
      @click="handleSelectTime(item, index)"
      :class="selectedTimeAMIndex === index ? 'bg-theme-blue text-white' : 'bg-gray-100'"
      class="px-3.5 py-2 text-sm text-gray-500 transition-all duration-300 box-border border border-gray-200 rounded-lg flex items-center justify-center"
    >
      {{ item.time }}
    </view>
  </view>
</template>

<script setup lang="ts">
  const props = withDefaults(
    defineProps<{
      times: HomeTypes.InboundTimeItem[];
      type: 'am' | 'pm';
      currentId: string;
    }>(),
    {
      times: () => [],
      type: 'am',
      currentId: '',
    },
  );

  const selectedTimeIndex = ref(0);

  const amTimes = ref<HomeTypes.InboundTimeItem[]>([]);
  const pmTimes = ref<HomeTypes.InboundTimeItem[]>([]);

  const emit = defineEmits<{
    (e: 'selectTime', item: HomeTypes.InboundTimeItem, index: number): void;
  }>();

  const handleSelectTime = (item: HomeTypes.InboundTimeItem, index: number) => {
    selectedTimeIndex.value = index;
    emit('selectTime', item, index);
  };

  const getTimes = async (id: string) => {
    try {
      // 获取日期详细信息
      const res = await monkey.$api.authine.getAuthineForm({
        schemaCode: 'crkyysj',
        objectId: id,
      });

      if (res.errcode !== 0 || !res.data?.bizObject?.data) {
        return monkey.$helper.toast.error('获取日期信息失败');
      }

      const data = res.data.bizObject.data;

      // 验证日期状态
      if (data.sfqy_key !== 'qy') {
        return monkey.$helper.toast.error('该日期已禁用');
      }

      const today = monkey.$dayjs();
      const selectedDateObj = monkey.$dayjs(date.dateStr);
      if (selectedDateObj.isBefore(today, 'day')) {
        return monkey.$helper.toast.error('该日期已过期');
      }

      // 处理时间段数据的通用函数
      const mapTimeSlots = (timeSlots: any[]): HomeTypes.InboundTimeItem[] => {
        return timeSlots.map((item) => ({
          time: monkey.$dayjs(`${date.dateStr} ${item.kssj}`).format('HH:mm') + '-' + monkey.$dayjs(`${date.dateStr} ${item.jssj}`).format('HH:mm'),
          count: Math.max(0, item.zdyys - item.yys), // 确保数量不为负数
          id: item.id,
          isDisabled: item.sfqy_key !== 'qy',
          isExpired: monkey.$dayjs(`${date.dateStr} ${item.jssj}`).isBefore(today),
        }));
      };

      // 设置上午和下午时间段
      amTimes.value = data.amzb ? mapTimeSlots(data.amzb) : [];
      pmTimes.value = data.pmzb ? mapTimeSlots(data.pmzb) : [];

      console.log('🚀 ~ handleSelectDate ~ 选中日期:', date.dateStr, '时间段数据:', {
        上午: amTimes.value,
        下午: pmTimes.value,
      });
    } catch (error) {
      console.error('handleSelectDate error:', error);
      monkey.$helper.toast.error('获取日期信息失败，请重试');
    }
  };

  onLoad(async () => {
    await getTimes(props.currentId);
  });
</script>

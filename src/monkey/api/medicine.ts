import request from '../request';

/**
 * 获取药材列表
 * @returns
 */
export const getMedicinalItemList = () => {
  return request({
    url: '/api/api/medicinal/getMedicinalList',
    method: 'GET',
  });
};

/**
 * 获取药材详情
 * @param id 药材ID
 * @returns
 */
export const getMedicinalItemDetail = (ycbm: string) => {
  return request({
    url: `/api/api/medicinal/getMedicinalPriceList?ycbm=${ycbm}`,
    method: 'GET',
  });
};

/**
 * 获取药材和价格列表 15个
 * @returns
 */
export const getMedicinalAndPriceList = () => {
  return request({
    url: `/api/api/medicinal/getMedicinalAndPriceList`,
    method: 'GET',
  });
};
